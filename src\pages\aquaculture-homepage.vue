<template>
  <div class="aquaculture-dashboard">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <h1 class="main-title">广西银海集团智能化养殖平台</h1>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 天气情况 -->
        <div class="panel">
          <div class="panel-header">
            <h3 class="panel-title">区域天气情况</h3>
          </div>
          <WeatherWidget />
        </div>

        <!-- 生产情况 -->
        <div class="panel h-0 flex-1">
          <div class="panel-header">
            <h3 class="panel-title">生产情况</h3>
          </div>
          <ProductionTable :building-id="currentBuildingId" />
        </div>

        <!-- 水质监测 -->
        <div class="panel chart-panel flex-2">
          <div class="panel-header">
            <h3 class="panel-title">水质监测</h3>
          </div>
          <WaterQualityMonitor :building-id="currentBuildingId" />
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="center-section">
        <!-- 统计信息 -->
        <div class="panel workshop-environment">
          <h3 class="panel-title">年度累计统计</h3>
          <div class="stats-display">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-flash text-blue-300"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">总耗电量</div>
                <div class="stat-value stat-value-power">
                  {{ yearlyStats.totalPower }}<span class="stat-unit">kWh</span>
                </div>
              </div>
            </div>

            <div class="environment-divider"></div>

            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-food text-blue-400"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">投喂量</div>
                <div class="stat-value stat-value-feed">
                  {{ yearlyStats.totalFeed }}<span class="stat-unit">kg</span>
                </div>
              </div>
            </div>

            <div class="environment-divider"></div>

            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-scale text-blue-500"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">总产量</div>
                <div class="stat-value stat-value-output">
                  {{ yearlyStats.totalOutput }}<span class="stat-unit">kg</span>
                </div>
              </div>
            </div>

            <div class="environment-divider"></div>

            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-water-pump text-cyan-400"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">抽水量</div>
                <div class="stat-value stat-value-pump-in">
                  {{ yearlyStats.totalPumpIn }}<span class="stat-unit">m³</span>
                </div>
              </div>
            </div>

            <div class="environment-divider"></div>

            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-pipe text-blue-600"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">排水量</div>
                <div class="stat-value stat-value-pump-out">
                  {{ yearlyStats.totalPumpOut
                  }}<span class="stat-unit">m³</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="panel buildings-panel">
          <img
            src="@/assets/images/lou.jpg"
            class="h-full w-full cursor-pointer object-cover transition-opacity hover:opacity-80"
            @click="handleSwitchToDashboard2"
          />
          <!-- <ThreeJSLouViewer /> -->
        </div>

        <!-- 监控画面 -->
        <div class="panel video-panel">
          <div class="video-content">
            <div class="monitor-grid">
              <div class="monitor-item">
                <div class="monitor-title">生产楼宇监控</div>
                <div class="monitor-screen">
                  <img
                    src="https://img2.baidu.com/it/u=2758257300,3203349391&fm=253&fmt=auto&app=138&f=JPEG?w=666&h=500"
                    alt="生产楼宇监控"
                  />
                </div>
              </div>
              <div class="monitor-item">
                <div class="monitor-title">园区监控</div>
                <div class="monitor-screen">
                  <img
                    src="https://img1.baidu.com/it/u=2320755174,666894318&fm=253&fmt=auto&app=120&f=JPEG?w=680&h=500"
                    alt="园区监控"
                  />
                </div>
              </div>
              <div class="monitor-item">
                <div class="monitor-title">闸坝监控</div>
                <div class="monitor-screen">
                  <img
                    src="https://img0.baidu.com/it/u=2980929902,1493845588&fm=253&fmt=auto&app=138&f=JPEG?w=781&h=500"
                    alt="闸坝监控"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 设备状态 -->
        <div class="panel">
          <div class="panel-header">
            <h3 class="panel-title">设备状态</h3>
          </div>
          <DeviceStatus :building-id="currentBuildingId" />
        </div>

        <!-- 虾苗生长趋势 -->
        <div class="panel chart-panel">
          <div class="panel-header">
            <h3 class="panel-title">虾苗生长趋势</h3>
          </div>
          <ShrimpGrowthTrend :building-id="currentBuildingId" />
        </div>

        <!-- 水下高清摄像头 -->
        <div class="panel">
          <h3 class="mb-2">水下高清摄像头</h3>
          <div class="underwater-camera">
            <img
              src="https://img0.baidu.com/it/u=202967768,2443417331&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=667"
              alt="水下高清摄像头"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import dayjs from "dayjs";
import WeatherWidget from "../components/WeatherWidget.vue";
import ProductionTable from "../components/ProductionTable.vue";
import WaterQualityMonitor from "../components/WaterQualityMonitor.vue";
import DeviceStatus from "../components/DeviceStatus.vue";
import ShrimpGrowthTrend from "../components/ShrimpGrowthTrend.vue";
import ThreeJSLouViewer from "../components/ThreeJSLouViewer.vue";

// 定义事件发射器
const emit = defineEmits<{
  "switch-to-dashboard2": [];
}>();

// 处理切换到 dashboard2 的点击事件
const handleSwitchToDashboard2 = () => {
  emit("switch-to-dashboard2");
};

// 当前时间
const currentTime = ref("");
const updateTime = () => {
  currentTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss");
};

// 当前选中的楼栋
const currentBuildingId = ref(1);

// 年度统计数据
const yearlyStats = ref({
  totalPower: "1,234,567",
  totalFeed: "456,789",
  totalOutput: "789,123",
  totalPumpIn: "2,345,678",
  totalPumpOut: "1,876,543",
});

// 19栋楼数据
// const buildings = ref([
//   {
//     id: 1,
//     name: "1#楼",
//     currentPower: 125.6,
//     poolCount: 24,
//     expectedOutput: 15600,
//   },
//   {
//     id: 2,
//     name: "2#楼",
//     currentPower: 132.4,
//     poolCount: 24,
//     expectedOutput: 16200,
//   },
//   {
//     id: 3,
//     name: "3#楼",
//     currentPower: 118.9,
//     poolCount: 20,
//     expectedOutput: 14800,
//   },
//   {
//     id: 4,
//     name: "4#楼",
//     currentPower: 145.2,
//     poolCount: 28,
//     expectedOutput: 17400,
//   },
//   {
//     id: 5,
//     name: "5#楼",
//     currentPower: 128.7,
//     poolCount: 24,
//     expectedOutput: 15900,
//   },
//   {
//     id: 6,
//     name: "6#楼",
//     currentPower: 135.1,
//     poolCount: 26,
//     expectedOutput: 16800,
//   },
//   {
//     id: 7,
//     name: "7#楼",
//     currentPower: 122.3,
//     poolCount: 22,
//     expectedOutput: 15200,
//   },
//   {
//     id: 8,
//     name: "8#楼",
//     currentPower: 140.8,
//     poolCount: 26,
//     expectedOutput: 17100,
//   },
//   {
//     id: 9,
//     name: "9#楼",
//     currentPower: 129.5,
//     poolCount: 24,
//     expectedOutput: 16000,
//   },
// ]);

// 选择楼栋
// const selectBuilding = (buildingId: number) => {
//   currentBuildingId.value = buildingId;
// };

// 定时器
let timeTimer: number;
let buildingTimer: number;

onMounted(() => {
  // 更新时间
  updateTime();
  timeTimer = setInterval(updateTime, 1000);

  // 定时切换楼栋
  buildingTimer = setInterval(() => {
    const nextId =
      currentBuildingId.value >= 19 ? 1 : currentBuildingId.value + 1;
    currentBuildingId.value = nextId;
  }, 10000); // 每10秒切换一次
});

onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer);
  if (buildingTimer) clearInterval(buildingTimer);
});
</script>

<style scoped>
.aquaculture-dashboard {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
  color: #66ccff;
  font-family: "Microsoft YaHei", sans-serif;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.aquaculture-dashboard::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(102, 204, 255, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(0, 150, 255, 0.12) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 60%,
      rgba(51, 153, 255, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

/* 顶部标题栏 */
.header-section {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 40px;
  background: linear-gradient(
    90deg,
    rgba(102, 204, 255, 0.15) 0%,
    rgba(102, 204, 255, 0.08) 50%,
    rgba(102, 204, 255, 0.15) 100%
  );
  border-bottom: 2px solid rgba(102, 204, 255, 0.4);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

.header-section::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #66ccff 50%,
    transparent 100%
  );
  box-shadow: 0 0 15px #66ccff;
}

.main-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 25px rgba(102, 204, 255, 0.9);
  letter-spacing: 2px;
}

/* 主内容区域 */
.main-content {
  height: 0;
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.left-section,
.right-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.center-section {
  flex: 1.5;
  width: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 面板通用样式 */
.panel {
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.12) 0%,
    rgba(102, 204, 255, 0.06) 100%
  );
  border: 1px solid rgba(102, 204, 255, 0.4);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #66ccff 50%,
    transparent 100%
  );
  box-shadow: 0 0 12px #66ccff;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.panel-title {
  font-size: 18px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 12px rgba(102, 204, 255, 0.9);
  text-align: center;
  flex-shrink: 0;
}

/* 统计信息样式 */
.workshop-environment {
  flex-shrink: 0;
  height: auto;
  min-height: 120px;
}

.stats-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  gap: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 2px solid rgba(102, 204, 255, 0.6);
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.stat-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(102, 204, 255, 0.25) 0%,
    transparent 70%
  );
  animation: iconGlow 2s ease-in-out infinite alternate;
}

@keyframes iconGlow {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.stat-icon i {
  font-size: 20px;
  position: relative;
  z-index: 1;
  text-shadow: 0 0 8px rgba(102, 204, 255, 0.8);
  filter: drop-shadow(0 0 4px rgba(102, 204, 255, 0.6));
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label {
  font-size: 12px;
  color: #66ccff;
  opacity: 0.9;
  white-space: nowrap;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #33aaff;
  white-space: nowrap;
}

.stat-unit {
  font-size: 14px;
  font-weight: normal;
  margin-left: 2px;
}

/* 年度累计统计数值的特殊高亮颜色 */
.stat-value-power {
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-feed {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-output {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-pump-in {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-pump-out {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.environment-divider {
  width: 1px;
  height: 50px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    #66ccff 20%,
    #66ccff 80%,
    transparent 100%
  );
  position: relative;
  flex-shrink: 0;
}

.environment-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: #66ccff;
  border-radius: 50%;
  box-shadow: 0 0 8px #66ccff;
  animation: dividerPulse 2s ease-in-out infinite;
}

@keyframes dividerPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.7;
  }
}

/* 楼栋选择样式 */
.buildings-panel {
  flex: 1;
  height: 0;
  padding: 0;
  border: none;
}

.buildings-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  flex: 1;
}

.building-item {
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.12) 0%,
    rgba(102, 204, 255, 0.06) 100%
  );
  border: 2px solid rgba(102, 204, 255, 0.4);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 60px;
}

.building-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(102, 204, 255, 0.15) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.building-item:hover::before {
  transform: translateX(100%);
}

.building-item:hover {
  border-color: #66ccff;
  box-shadow: 0 0 25px rgba(102, 204, 255, 0.6);
  transform: scale(1.05);
}

.building-item.active {
  background: linear-gradient(
    135deg,
    rgba(51, 170, 255, 0.25) 0%,
    rgba(51, 170, 255, 0.15) 100%
  );
  border-color: #33aaff;
  box-shadow: 0 0 30px rgba(51, 170, 255, 0.7);
  animation: activeBuilding 2s infinite;
}

@keyframes activeBuilding {
  0%,
  100% {
    box-shadow: 0 0 30px rgba(51, 170, 255, 0.7);
  }
  50% {
    box-shadow: 0 0 40px rgba(51, 170, 255, 0.9);
  }
}

.building-number {
  font-size: 18px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 12px rgba(102, 204, 255, 0.9);
}

.building-item.active .building-number {
  color: #33aaff;
  text-shadow: 0 0 15px rgba(51, 170, 255, 0.9);
}

.building-popup {
  position: absolute;
  top: -120px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 34, 68, 0.95);
  border: 1px solid #33aaff;
  border-radius: 8px;
  padding: 15px;
  min-width: 200px;
  z-index: 10;
}

.popup-content h4 {
  margin: 0 0 10px 0;
  color: #33aaff;
  text-align: center;
}

.popup-stats div {
  margin: 5px 0;
  font-size: 14px;
  color: #66ccff;
}

/* 监控画面样式 */
.monitor-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.monitor-item {
  display: flex;
  flex-direction: column;
}

.monitor-title {
  text-align: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #66ccff;
}

.monitor-screen {
  border: 1px solid rgba(102, 204, 255, 0.4);
  border-radius: 4px;
  overflow: hidden;
  height: 180px;
}

.monitor-screen img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8) contrast(1.2) hue-rotate(10deg);
}

/* 水下摄像头样式 */
.underwater-camera {
  height: 180px;
  border: 1px solid rgba(102, 204, 255, 0.4);
  border-radius: 4px;
  overflow: hidden;
}

.underwater-camera img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8) contrast(1.2) hue-rotate(10deg);
}

/* 图表面板 */
.chart-panel {
  flex: 1;
  height: 0;
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.panel {
  animation: fadeInUp 0.6s ease-out;
}

.panel:nth-child(1) {
  animation-delay: 0.1s;
}

.panel:nth-child(2) {
  animation-delay: 0.2s;
}

.panel:nth-child(3) {
  animation-delay: 0.3s;
}
</style>
